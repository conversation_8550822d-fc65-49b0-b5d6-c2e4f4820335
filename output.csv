﻿一级功能模块,二级功能模块,三级功能模块,功能描述,预估工作量（人天）,功能用户,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP
密码资产数据管理,密码资产数据管理,密码服务数据库新增,新增数据库信息，选择数据库类型，输入数据库IP、端口，管理员账号、密码,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击新增数据库按钮,新增数据库信息,选择数据库类型,E,数据库类型信息,数据库类型,1
,,,,,,,,输入数据库IP和端口,E,数据库连接信息,数据库IP、端口,1
,,,,,,,,输入管理员账号和密码,E,数据库认证信息,管理员账号、密码,1
,,,,,,,,保存数据库信息,W,数据库信息,数据库类型、数据库IP、端口、管理员账号、密码,1
,,密码服务数据库列表,列表展示数据库名称、数据库类型、实例库名称、数据库ip端口、完整性校验。,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击数据库列表菜单,查看数据库列表,查询数据库列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取数据库列表信息,R,数据库列表信息,数据库名称、数据库类型、实例库名称、数据库IP端口、完整性校验,1
,,,,,,,,展示数据库列表,X,数据库列表信息,数据库名称、数据库类型、实例库名称、数据库IP端口、完整性校验,1
,,,,,,,,返回数据库列表分页结果,X,分页结果,总记录数、当前页数据,1
,,密码服务数据库模式列表,列表展示密码服务数据库模式,2.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击数据库模式列表菜单,查看数据库模式列表,查询数据库模式列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取数据库模式列表信息,R,数据库模式列表信息,数据库模式名称、所属数据库、创建时间,1
,,密码服务数据库模式删除,删除密码服务数据库模式,2.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击删除数据库模式按钮,删除数据库模式,确认删除数据库模式,E,数据库模式标识,数据库模式ID,1
,,,,,,,,执行数据库模式删除操作,W,数据库模式信息,数据库模式ID,1
,,密码服务数据库模式查询,查询密码服务数据库模式,2.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员输入查询条件并点击查询按钮,查询数据库模式,输入查询条件,E,查询条件,数据库模式名称、所属数据库,1
,,,,,,,,执行数据库模式查询操作,R,数据库模式列表信息,数据库模式名称、所属数据库、创建时间,1
,,密码服务数据库模式新增,新增数据库模式,2.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击新增数据库模式按钮,新增数据库模式,输入数据库模式信息,E,数据库模式信息,数据库模式名称、所属数据库,1
,,,,,,,,保存数据库模式信息,W,数据库模式信息,数据库模式名称、所属数据库、创建时间,1
,,API网关列表,列表内容：名称、所属区域、标识、类型、IP、业务端口、管理端口、区域内IP（列表不显示）、端口（列表不显示）、反向代理地址端口（列表不显示）,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击API网关列表菜单,查看API网关列表,查询API网关列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取API网关列表信息,R,API网关列表信息,名称、所属区域、标识、类型、IP、业务端口、管理端口,1
,,,,,,,,展示API网关列表,X,API网关列表信息,名称、所属区域、标识、类型、IP、业务端口、管理端口,1
,,,,,,,,返回API网关列表分页结果,X,分页结果,总记录数、当前页数据,1
,,API网关初始化,密码服务平台部署成功后，如选择部署API网关，根据平台部署信息，自动加载对应的部署网关信息,3.0,发起者：系统，接收者：密服平台-密码资产数据管理模块,密码服务平台部署成功并选择部署API网关,初始化API网关信息,读取平台部署信息,R,平台部署信息,部署区域、部署时间、部署版本,1
,,,,,,,,生成默认API网关配置,E,API网关配置信息,网关名称、所属区域、标识、类型、管理端口,1
,,,,,,,,保存初始化的API网关信息,W,API网关信息,网关名称、所属区域、标识、类型、管理端口,1
,,API网关新增,新增API网关信息，录入名称、所属需求、标识、类型（管理、业务）、管理端口,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击新增API网关按钮,新增API网关信息,输入API网关基本信息,E,API网关基本信息,名称、所属需求、标识、类型,1
,,,,,,,,输入管理端口信息,E,API网关端口信息,管理端口,1
,,,,,,,,保存新增的API网关信息,W,API网关信息,名称、所属需求、标识、类型、管理端口,1
,,API网关删除,删除网关信息,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击删除API网关按钮,删除API网关信息,确认删除API网关,E,API网关标识,网关ID,1
,,,,,,,,执行API网关删除操作,W,API网关信息,网关ID,1
,,路由管理列表,展示内容：路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击路由管理列表菜单,查看路由管理列表,查询路由管理列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取路由管理列表信息,R,路由管理列表信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1
,,,,,,,,展示路由管理列表,X,路由管理列表信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1
,,,,,,,,返回路由管理列表分页结果,X,分页结果,总记录数、当前页数据,1
,,路由管理详情,展示路由管理详情，包含服务列表信息、应用信息,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击路由管理详情按钮,查看路由管理详情,查询路由管理详情信息,R,路由管理详情信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间,1
,,,,,,,,读取服务列表信息,R,服务列表信息,服务名称、服务状态,1
,,,,,,,,读取应用信息,R,应用信息,应用名称、应用描述,1
,,,,,,,,展示路由管理详情,X,路由管理详情信息,路由名称、路由组件标识、服务类型、所属应用、所属服务组、URL路径、上游配置、匹配条件、超时时间、服务列表信息、应用信息,1
,,设备类型展示,内容：设备类型名称、所属厂商（录入）、设备类型（云密码机、物理密码机、虚拟密码机）、管理接口协同（HTTPS、HTTP）、管理端口；云密码机、虚拟密码机、物理密码机类型配置信息不同,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击设备类型展示菜单,查看设备类型列表,查询设备类型列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取设备类型列表信息,R,设备类型列表信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,,,,,,,展示设备类型列表,X,设备类型列表信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,设备类型初始化,根据平台支持的设备类型，平台部署时，初始化平台默认支持的设备类型,4.0,发起者：系统，接收者：密服平台-密码资产数据管理模块,平台部署时,初始化设备类型信息,读取平台支持的设备类型,R,平台支持设备类型信息,设备类型列表,1
,,,,,,,,生成默认设备类型配置,E,设备类型配置信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,,,,,,,保存初始化的设备类型信息,W,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,设备类型新增,添加设备对应信息,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击新增设备类型按钮,新增设备类型信息,输入设备类型基本信息,E,设备类型基本信息,设备类型名称、所属厂商、设备类型,1
,,,,,,,,输入管理接口协同信息,E,设备管理接口信息,管理接口协同、管理端口,1
,,,,,,,,保存新增的设备类型信息,W,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,设备类型编辑,编辑设备相关信息,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击编辑设备类型按钮,编辑设备类型信息,读取设备类型当前信息,R,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,,,,,,,修改设备类型信息,E,设备类型更新信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,,,,,,,保存修改后的设备类型信息,W,设备类型信息,设备类型名称、所属厂商、设备类型、管理接口协同、管理端口,1
,,设备类型停用,停用设备类型不可再创建该类型的设备,2.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击停用设备类型按钮,停用设备类型,确认停用设备类型,E,设备类型标识,设备类型ID,1
,,,,,,,,更新设备类型状态为停用,W,设备类型状态信息,设备类型ID、状态,1
,,设备类型启用,启用停用的设备类型,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击启用设备类型按钮,启用设备类型,确认启用设备类型,E,设备类型标识,设备类型ID,1
,,,,,,,,更新设备类型状态为启用,W,设备类型状态信息,设备类型ID、状态,1
,,设备类型删除,当无该类型的设备时，删除对应设备类型,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击删除设备类型按钮,删除设备类型,确认删除设备类型,E,设备类型标识,设备类型ID,1
,,,,,,,,执行设备类型删除操作,W,设备类型信息,设备类型ID,1
,,监控信息配置查看,查询当前监控信息的配置信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击监控信息配置查看菜单,查看监控信息配置,查询当前监控信息配置,R,监控信息配置,监控方式、配置详情,1
,,,,,,,,展示监控信息配置详情,X,监控信息配置详情,监控方式、配置详情,1
,,监控信息配置,配置设备类型的监控信息，分为不同监控方式（包含SNMP、Rest接口、监控组件、监控探针）,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击监控信息配置按钮,配置监控信息,选择监控方式,E,监控方式信息,监控方式,1
,,,,,,,,输入监控配置详情,E,监控配置详情,配置参数,1
,,,,,,,,保存监控信息配置,W,监控信息配置,监控方式、配置详情,1
,,密码设备集群列表,列表内容：名称、设备类型、所属区域、设备数量、描述,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击密码设备集群列表菜单,查看密码设备集群列表,查询密码设备集群列表分页信息,E,分页信息,页码、单页数量,1
,,,,,,,,读取密码设备集群列表信息,R,密码设备集群列表信息,名称、设备类型、所属区域、设备数量、描述,1
,,,,,,,,展示密码设备集群列表,X,密码设备集群列表信息,名称、设备类型、所属区域、设备数量、描述,1
,,密码设备集群新增,创建密码设备集群，录入名称、选择设备类型、所属区域、描述,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击新增密码设备集群按钮,新增密码设备集群,输入密码设备集群基本信息,E,密码设备集群基本信息,名称、设备类型、所属区域、描述,1
,,,,,,,,保存新增的密码设备集群信息,W,密码设备集群信息,名称、设备类型、所属区域、设备数量、描述,1
,,密码设备集群编辑,可编辑内容：名称、描述,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击编辑密码设备集群按钮,编辑密码设备集群,读取密码设备集群当前信息,R,密码设备集群信息,集群ID、名称、描述,1
,,,,,,,,修改密码设备集群信息,E,密码设备集群更新信息,名称、描述,1
,,,,,,,,保存修改后的密码设备集群信息,W,密码设备集群信息,集群ID、名称、描述,1
,,密码设备集群删除,删除密码设备集群，需保障密码设备集群未被密码服务调用,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击删除密码设备集群按钮,删除密码设备集群,确认删除密码设备集群,E,密码设备集群标识,集群ID,1
,,,,,,,,执行密码设备集群删除操作,W,密码设备集群信息,集群ID,1
,,绑定密码设备,根据密码设备类型绑定密码设备，绑定密码设备后，根据类型配置判断是否需要进行保护密钥的创建和同步,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击绑定密码设备按钮,绑定密码设备,选择密码设备类型,E,密码设备类型信息,设备类型,1
,,,,,,,,选择密码设备,E,密码设备信息,设备ID,1
,,,,,,,,执行绑定操作,W,设备绑定关系,设备ID、集群ID,1
,,释放密码设备,释放密码设备和密码设备集群的绑定关系，最后一个设备释放时，需保障密码设备集群未被密码服务调用,3.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击释放密码设备按钮,释放密码设备,确认释放密码设备,E,密码设备标识,设备ID,1
,,,,,,,,解除设备与集群的绑定关系,W,设备绑定关系,设备ID、集群ID,1
,,云密码机列表,云密码机列表页面中，在搜索框中输入名称和管理IP，可以模糊查询云密码机列表,4.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员在搜索框中输入名称和管理IP并点击查询按钮,查询云密码机列表,输入查询条件,E,查询条件,名称、管理IP,1
,,,,,,,,执行云密码机列表查询,R,云密码机列表信息,云密码机名称、管理IP、状态,1
,,,,,,,,展示云密码机列表,X,云密码机列表信息,云密码机名称、管理IP、状态,1
,,云密码机新建,云密码机管理页面中，点击“新建”按钮，打开添加云密码机页面。输入云密码机信息，点击“确定”按钮添加云密码机。管理ip和管理端口根据实际设备部署情况获取,5.0,发起者：管理员，接收者：密服平台-密码资产数据管理模块,管理员点击新建云密码机按钮,新建云密码机,打开添加云密码机页面,E,页面操作信息,操作类型,1
,,,,,,,,输入云密码机信息,E,云密码机信息,名称、管理IP、管理端口,1
,,,,,,,,保存新增的云密码机信息,W,云密码机信息,名称、管理IP、管理端口,1
,,,,,,,,获取实际设备部署信息,R,设备部署信息,管理IP、管理端口,1
,,云密码机编辑,云密码机信息列表，点击右侧操作列“编辑”按钮，打开编辑云密码机信息页面，修改云密码机名称和备注，点击“确定”按钮保存云密码机信息。,5.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在云密码机列表页点击右侧操作列“编辑”按钮,打开编辑云密码机信息页面,查询云密码机原始信息,R,云密码机信息,云密码机ID、名称、备注,1
,,,,,,,,展示云密码机编辑页面,X,云密码机信息,云密码机ID、名称、备注,1
,,,,,,,,接收用户输入的更新信息,E,云密码机信息,云密码机ID、名称、备注,1
,,,,,,,,校验输入信息合法性,R,云密码机信息,云密码机ID、名称、备注,1
,,,,,,,,更新云密码机信息,W,云密码机信息,云密码机ID、名称、备注,1
,,云密码机删除,云密码机列表页，点击右侧操作列 更多->“删除”按钮，在弹出框中点击“确定”按钮删除云密码机。系统中存在使用该云密码机生成的虚拟机时，云密码机不能删除。,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在云密码机列表页点击右侧操作列“删除”按钮,检查云密码机是否被使用,查询云密码机使用状态,R,云密码机使用状态,云密码机ID、是否被使用,1
,,,,,,,,展示删除确认弹窗,X,删除确认信息,确认提示信息,1
,,,,,,,,接收删除确认请求,E,删除请求,云密码机ID,1
,,,,,,,,执行删除操作,W,云密码机信息,云密码机ID,1
,,云密码机详情,云密码机信息列表，点击右侧操作列“详情”按钮，打开 云密码机详情页面。,5.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在云密码机列表页点击右侧操作列“详情”按钮,展示云密码机详情信息,查询云密码机详细信息,R,云密码机详情,云密码机ID、名称、备注、管理IP、管理端口、版本、序列号,1
,,,,,,,,展示云密码机详情页面,X,云密码机详情,云密码机ID、名称、备注、管理IP、管理端口、版本、序列号,1
,,网络配置列表,查看为云密码机配置的虚机网络,2.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户进入云密码机网络配置页面,查看虚机网络配置列表,查询虚机网络配置信息,R,虚机网络配置,网络配置ID、管理IP范围、业务IP范围、所属云密码机,1
,,,,,,,,展示虚机网络配置列表,X,虚机网络配置,网络配置ID、管理IP范围、业务IP范围、所属云密码机,1
,,新增虚拟机网络配置,配置云密码机虚拟出的虚拟密码机的管理IP和业务IP范围，创建虚拟密码机时，从该范围内自动获取IP和端口,6.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击新增虚拟机网络配置按钮,打开新增网络配置页面,初始化网络配置页面,R,网络配置模板,默认配置项,1
,,,,,,,,展示新增网络配置页面,X,网络配置模板,默认配置项,1
,,,,,,,,接收用户输入的网络配置信息,E,网络配置信息,管理IP范围、业务IP范围、所属云密码机,1
,,,,,,,,校验网络配置信息合法性,R,网络配置信息,管理IP范围、业务IP范围、所属云密码机,1
,,,,,,,,保存网络配置信息,W,网络配置信息,管理IP范围、业务IP范围、所属云密码机,1
,,,,,,,,返回保存结果,X,保存结果,成功/失败状态,1
,,,,,,,,更新网络配置列表,R,网络配置列表,所有网络配置项,1
,,,,,,,,展示更新后的网络配置列表,X,网络配置列表,所有网络配置项,1
,,批量创建虚拟机,批量创建虚拟密码机，自动加载虚机网络，支持配置虚机资源。调用云密码机0088标准创建虚机并自动配置网络。,10.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击批量创建虚拟机按钮,打开批量创建虚拟机页面,加载云密码机列表,R,云密码机列表,云密码机ID、名称,1
,,,,,,,,展示批量创建虚拟机页面,X,云密码机列表,云密码机ID、名称,1
,,,,,,,,接收用户选择的云密码机和资源配置,E,虚拟机创建请求,云密码机ID、资源配置、数量,1
,,,,,,,,校验资源配置合法性,R,资源配置,资源配置详情,1
,,,,,,,,调用云密码机0088标准接口创建虚拟机,W,虚拟机信息,虚拟机ID、云密码机ID、资源配置,1
,,,,,,,,自动配置虚拟机网络,W,虚拟机网络配置,虚拟机ID、管理IP、业务IP,1
,,,,,,,,返回创建结果,X,创建结果,成功/失败状态、虚拟机列表,1
,,虚拟密码机列表,平台中云机虚拟出的VSM,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户进入虚拟密码机管理页面,展示虚拟密码机列表,查询虚拟密码机列表信息,R,虚拟密码机列表,虚拟机ID、名称、所属云密码机、状态,1
,,,,,,,,展示虚拟密码机列表,X,虚拟密码机列表,虚拟机ID、名称、所属云密码机、状态,1
,,虚拟密码机列表查询,支持根据名称、主机、管理ip、服务ip、设备类型进行查询,2.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页输入查询条件并点击查询按钮,执行虚拟密码机列表查询,接收用户输入的查询条件,E,查询条件,名称、主机、管理IP、服务IP、设备类型,1
,,,,,,,,根据查询条件筛选虚拟密码机列表,R,虚拟密码机列表,虚拟机ID、名称、主机、管理IP、服务IP、设备类型,1
,,,,,,,,展示查询结果,X,虚拟密码机列表,虚拟机ID、名称、主机、管理IP、服务IP、设备类型,1
,,创建虚拟密码机,选择云密码机，批量创建虚拟密码机，和云机管理中批量创建密码机一致,2.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击创建虚拟密码机按钮,打开创建虚拟密码机页面,加载云密码机列表,R,云密码机列表,云密码机ID、名称,1
,,,,,,,,展示创建虚拟密码机页面,X,云密码机列表,云密码机ID、名称,1
,,,,,,,,接收用户选择的云密码机,E,创建请求,云密码机ID,1
,,,,,,,,调用批量创建接口创建虚拟密码机,W,虚拟机信息,虚拟机ID、云密码机ID,1
,,虚拟密码机详情,虚拟密码机详情,5.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“详情”按钮,展示虚拟密码机详情信息,查询虚拟密码机详细信息,R,虚拟密码机详情,虚拟机ID、名称、所属云密码机、管理IP、服务IP、状态,1
,,,,,,,,展示虚拟密码机详情页面,X,虚拟密码机详情,虚拟机ID、名称、所属云密码机、管理IP、服务IP、状态,1
,,编辑虚拟密码机,编辑虚拟密码机名称、连接密码，并在动态下发给密码服务,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“编辑”按钮,打开编辑虚拟密码机页面,查询虚拟密码机原始信息,R,虚拟密码机信息,虚拟机ID、名称、连接密码,1
,,,,,,,,展示虚拟密码机编辑页面,X,虚拟密码机信息,虚拟机ID、名称、连接密码,1
,,,,,,,,接收用户输入的更新信息,E,虚拟密码机信息,虚拟机ID、名称、连接密码,1
,,,,,,,,校验输入信息合法性,R,虚拟密码机信息,虚拟机ID、名称、连接密码,1
,,,,,,,,更新虚拟密码机信息,W,虚拟密码机信息,虚拟机ID、名称、连接密码,1
,,,,,,,,动态下发配置到密码服务,W,密码服务配置,虚拟机ID、配置信息,1
,,删除虚拟密码机,删除虚拟密码机,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“删除”按钮,执行删除虚拟密码机操作,接收删除请求,E,删除请求,虚拟机ID,1
,,,,,,,,执行删除操作,W,虚拟机信息,虚拟机ID,1
,,启动虚拟密码机,启动虚拟密码机,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“启动”按钮,执行启动虚拟密码机操作,接收启动请求,E,启动请求,虚拟机ID,1
,,,,,,,,执行启动操作,W,虚拟机状态,虚拟机ID、状态,1
,,停止虚拟密码机,停止虚拟密码机,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“停止”按钮,执行停止虚拟密码机操作,接收停止请求,E,停止请求,虚拟机ID,1
,,,,,,,,执行停止操作,W,虚拟机状态,虚拟机ID、状态,1
,,重启虚拟密码机,重启虚拟密码机,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“重启”按钮,执行重启虚拟密码机操作,接收重启请求,E,重启请求,虚拟机ID,1
,,,,,,,,执行重启操作,W,虚拟机状态,虚拟机ID、状态,1
,,强制删除虚拟密码机,解决虚拟密码机已不存在，无法正常删除情况,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在虚拟密码机列表页点击“强制删除”按钮,执行强制删除虚拟密码机操作,接收强制删除请求,E,强制删除请求,虚拟机ID,1
,,,,,,,,执行强制删除操作,W,虚拟机信息,虚拟机ID,1
,,生成虚机影像,生成虚机影像,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击生成虚机影像按钮,执行生成虚机影像操作,接收生成影像请求,E,生成影像请求,虚拟机ID,1
,,,,,,,,执行生成影像操作,W,虚机影像,虚拟机ID、影像文件,1
,,下载虚机影像,下载虚机影像,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击下载虚机影像按钮,执行下载虚机影像操作,接收下载请求,E,下载请求,影像ID,1
,,,,,,,,返回影像文件下载链接,X,下载链接,影像文件下载地址,1
,,导入虚机影像,导入虚机影像，还原虚机影像,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击导入虚机影像按钮,执行导入虚机影像操作,接收导入影像请求,E,导入请求,影像文件,1
,,,,,,,,执行影像导入操作,W,虚机影像,影像ID、影像文件,1
,,物理密码机列表,物理密码机列表展示已经注册到系统中物理密码机信息，包含密码机的名称、所属厂商、设备类型、所属设备组、管理IP、管理端口、版本、序列号、完整性校验、备注等信息。,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户进入物理密码机管理页面,展示物理密码机列表,查询物理密码机列表信息,R,物理密码机列表,密码机ID、名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号,1
,,,,,,,,展示物理密码机列表,X,物理密码机列表,密码机ID、名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号,1
,,物理密码机新建,系统密码机是将机房已经上架部署完毕的密码机注册到系统中，交由管理平台进行统一管理。,5.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击新建物理密码机按钮,打开新建物理密码机页面,初始化物理密码机注册页面,R,注册模板,默认配置项,1
,,,,,,,,展示新建物理密码机页面,X,注册模板,默认配置项,1
,,,,,,,,接收用户输入的物理密码机信息,E,物理密码机信息,名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号、备注,1
,,,,,,,,校验物理密码机信息合法性,R,物理密码机信息,名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号、备注,1
,,,,,,,,保存物理密码机信息,W,物理密码机信息,密码机ID、名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号、备注,1
,,物理密码机编辑,在密码机列表页，点击右侧操作列“编辑”按钮，打开物理密码机编辑页面，修改物理密码机信息(可编辑名称、备注、连接密码)后，点击“确定”按钮保存编辑的信息,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在物理密码机列表页点击“编辑”按钮,打开编辑物理密码机页面,查询物理密码机原始信息,R,物理密码机信息,密码机ID、名称、备注、连接密码,1
,,,,,,,,展示物理密码机编辑页面,X,物理密码机信息,密码机ID、名称、备注、连接密码,1
,,,,,,,,接收用户输入的更新信息,E,物理密码机信息,密码机ID、名称、备注、连接密码,1
,,,,,,,,校验输入信息合法性,R,物理密码机信息,密码机ID、名称、备注、连接密码,1
,,,,,,,,更新物理密码机信息,W,物理密码机信息,密码机ID、名称、备注、连接密码,1
,,物理密码机删除,在密码机信息列表中可以删除已经注册到系统中的设备信息。,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在物理密码机列表页点击“删除”按钮,执行删除物理密码机操作,接收删除请求,E,删除请求,密码机ID,1
,,,,,,,,执行删除操作,W,物理密码机信息,密码机ID,1
,,物理密码机详情,在密码机列表页，点击右侧操作列“详情”按钮，系统打开密码机详情页面。,5.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在物理密码机列表页点击“详情”按钮,展示物理密码机详情信息,查询物理密码机详细信息,R,物理密码机详情,密码机ID、名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号、完整性校验、备注,1
,,,,,,,,展示物理密码机详情页面,X,物理密码机详情,密码机ID、名称、所属厂商、设备类型、管理IP、管理端口、版本、序列号、完整性校验、备注,1
,,强制删除,解决物理密码机已损坏，无法正常删除情况,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户在物理密码机列表页点击“强制删除”按钮,执行强制删除物理密码机操作,接收强制删除请求,E,强制删除请求,密码机ID,1
,,,,,,,,执行强制删除操作,W,物理密码机信息,密码机ID,1
,,管理页面跳转,支持根据配置的管理页面地址，单击按钮跳转到对应设备的管理页面,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击管理页面跳转按钮,跳转到设备管理页面,查询设备管理页面地址,R,管理页面地址,设备ID、管理页面URL,1
,,,,,,,,执行页面跳转,X,跳转链接,管理页面URL,1
,,保护主密钥创建,服务器密码机、虚拟服务器密码机、签名验签服务器的保护主密钥的创建,5.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击保护主密钥创建按钮,打开保护主密钥创建页面,加载设备列表,R,设备列表,设备ID、设备名称、设备类型,1
,,,,,,,,展示保护主密钥创建页面,X,设备列表,设备ID、设备名称、设备类型,1
,,,,,,,,接收创建请求,E,创建请求,设备ID,1
,,,,,,,,执行保护主密钥创建,W,保护主密钥,密钥ID、设备ID、密钥内容,1
,,保护主密钥同步,支持设备内保护主密钥的同步,4.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击保护主密钥同步按钮,执行保护主密钥同步操作,接收同步请求,E,同步请求,设备ID,1
,,,,,,,,执行保护主密钥同步,W,保护主密钥,密钥ID、设备ID、密钥内容,1
,,保护主密钥备份,将设备内保护主密钥的加密导出备份,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击保护主密钥备份按钮,执行保护主密钥备份操作,接收备份请求,E,备份请求,设备ID,1
,,,,,,,,执行保护主密钥备份,W,保护主密钥备份,备份ID、设备ID、备份文件,1
,,保护主密钥还原,还原设备内保护主密钥,3.0,发起者：用户，接收者：密服平台-密码资产数据管理模块,用户点击保护主密钥还原按钮,执行保护主密钥还原操作,接收还原请求,E,还原请求,备份ID,1
,,,,,,,,执行保护主密钥还原,W,保护主密钥,密钥ID、设备ID、密钥内容,1
