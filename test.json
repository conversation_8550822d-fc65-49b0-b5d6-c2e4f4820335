[{"添加密码知识库数据": [{"功能用户": "发起者：平台操作员，接收者：密码资产数据管理系统", "触发事件": "用户点击添加知识库记录按钮并上传文件", "功能过程": "添加一条知识库记录", "子过程": [{"子过程描述": "输入知识库元数据信息", "数据移动类型": "E", "数据组": "知识库元数据", "数据属性": "文件名、文件类型、文档分类", "CFP": 1}, {"子过程描述": "上传文件内容", "数据移动类型": "E", "数据组": "文件内容", "数据属性": "文件数据", "CFP": 1}, {"子过程描述": "保存知识库记录到数据库", "数据移动类型": "W", "数据组": "知识库记录", "数据属性": "文件名、文件类型、文档分类、文件路径", "CFP": 1}, {"子过程描述": "显示添加成功消息", "数据移动类型": "X", "数据组": "操作结果", "数据属性": "成功消息", "CFP": 1}]}]}, {"编辑密码知识库数据": [{"功能用户": "发起者：平台操作员，接收者：密码资产数据管理系统", "触发事件": "用户选择一条记录并点击编辑按钮", "功能过程": "编辑知识库记录", "子过程": [{"子过程描述": "输入要编辑的记录标识", "数据移动类型": "E", "数据组": "记录选择", "数据属性": "记录ID", "CFP": 1}, {"子过程描述": "读取现有记录信息", "数据移动类型": "R", "数据组": "知识库记录", "数据属性": "文件名、文件类型、文档分类", "CFP": 1}, {"子过程描述": "保存更新后的记录", "数据移动类型": "W", "数据组": "知识库记录", "数据属性": "文件名、文件类型、文档分类", "CFP": 1}]}]}, {"删除密码知识库数据": [{"功能用户": "发起者：平台操作员，接收者：密码资产数据管理系统", "触发事件": "用户选择一条记录并点击删除按钮", "功能过程": "删除知识库记录及文件", "子过程": [{"子过程描述": "输入删除确认信息", "数据移动类型": "E", "数据组": "删除请求", "数据属性": "记录ID", "CFP": 1}, {"子过程描述": "删除数据库记录", "数据移动类型": "W", "数据组": "知识库记录", "数据属性": "记录ID", "CFP": 1}, {"子过程描述": "删除关联文件", "数据移动类型": "W", "数据组": "文件数据", "数据属性": "文件路径", "CFP": 1}]}]}, {"查询密码知识库数据": [{"功能用户": "发起者：平台操作员，接收者：密码资产数据管理系统", "触发事件": "用户输入筛选条件并点击查询按钮", "功能过程": "查询知识库记录", "子过程": [{"子过程描述": "输入查询筛选条件", "数据移动类型": "E", "数据组": "查询参数", "数据属性": "文件类型、文档分类、文件名", "CFP": 1}, {"子过程描述": "检索匹配的记录列表", "数据移动类型": "R", "数据组": "知识库记录列表", "数据属性": "记录ID、文件名、文件类型", "CFP": 1}, {"子过程描述": "显示查询结果", "数据移动类型": "X", "数据组": "查询结果", "数据属性": "记录列表", "CFP": 1}]}]}, {"显示/隐藏知识库信息": [{"功能用户": "发起者：平台操作员，接收者：密码资产数据管理系统", "触发事件": "用户点击记录旁的显示/隐藏按钮", "功能过程": "配置记录显示状态", "子过程": [{"子过程描述": "选择要配置的记录", "数据移动类型": "E", "数据组": "记录选择", "数据属性": "记录ID", "CFP": 1}, {"子过程描述": "切换显示状态标志", "数据移动类型": "E", "数据组": "显示状态", "数据属性": "显示/隐藏标志", "CFP": 1}, {"子过程描述": "保存更新后的状态", "数据移动类型": "W", "数据组": "知识库记录", "数据属性": "显示状态", "CFP": 1}]}]}, {"预览知识库信息": [{"功能用户": "发起者：平台操作员，接收者：密码资产数据管理系统", "触发事件": "用户点击记录旁的预览按钮", "功能过程": "预览知识库内容", "子过程": [{"子过程描述": "输入预览请求", "数据移动类型": "E", "数据组": "预览请求", "数据属性": "记录ID", "CFP": 1}, {"子过程描述": "读取文件内容", "数据移动类型": "R", "数据组": "文件数据", "数据属性": "文件内容", "CFP": 1}, {"子过程描述": "渲染并显示预览内容", "数据移动类型": "X", "数据组": "预览内容", "数据属性": "渲染后的内容", "CFP": 1}]}]}]